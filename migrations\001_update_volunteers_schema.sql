-- Migration: Update volunteers table schema for multi-vector architecture
-- Date: 2025-07-05
-- Description: 
--   1. Change profile_data JSONB field to skills TEXT[] array
--   2. Add vector_metadata JSONB field for Qdrant relationship information
--   3. Add languages_spoken TEXT[] field for languages the volunteer speaks

-- Start transaction
BEGIN;

-- Step 1: Add new columns
ALTER TABLE public.volunteers 
ADD COLUMN IF NOT EXISTS skills TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS vector_metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS languages_spoken TEXT[] DEFAULT '{}';

-- Step 2: Migrate existing data from profile_data to new fields
-- Extract skills from profile_data if it exists and is structured correctly
UPDATE public.volunteers 
SET 
    skills = CASE 
        WHEN profile_data ? 'skills' AND jsonb_typeof(profile_data->'skills') = 'array' 
        THEN ARRAY(SELECT jsonb_array_elements_text(profile_data->'skills'))
        ELSE '{}'::TEXT[]
    END,
    languages_spoken = CASE 
        WHEN profile_data ? 'languages' AND jsonb_typeof(profile_data->'languages') = 'array' 
        THEN ARRAY(SELECT jsonb_array_elements_text(profile_data->'languages'))
        ELSE '{}'::TEXT[]
    END,
    vector_metadata = CASE 
        WHEN profile_data ? 'qdrant_metadata' 
        THEN profile_data->'qdrant_metadata'
        ELSE '{}'::JSONB
    END
WHERE profile_data IS NOT NULL AND profile_data != '{}'::JSONB;

-- Step 3: Create backup of old profile_data before dropping
-- Create a backup table for the old profile_data
CREATE TABLE IF NOT EXISTS public.volunteers_profile_data_backup AS 
SELECT id, email, profile_data, created_at 
FROM public.volunteers 
WHERE profile_data IS NOT NULL AND profile_data != '{}'::JSONB;

-- Step 4: Drop the old profile_data column
-- Note: This is commented out for safety. Uncomment after verifying migration
-- ALTER TABLE public.volunteers DROP COLUMN profile_data;

-- Step 5: Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_volunteers_skills ON public.volunteers USING GIN (skills);
CREATE INDEX IF NOT EXISTS idx_volunteers_languages_spoken ON public.volunteers USING GIN (languages_spoken);
CREATE INDEX IF NOT EXISTS idx_volunteers_vector_metadata ON public.volunteers USING GIN (vector_metadata);

-- Step 6: Add comments for documentation
COMMENT ON COLUMN public.volunteers.skills IS 'Array of skills extracted from LinkedIn profiles';
COMMENT ON COLUMN public.volunteers.vector_metadata IS 'Metadata for linking to Qdrant vector database records';
COMMENT ON COLUMN public.volunteers.languages_spoken IS 'Array of languages the volunteer speaks';

-- Step 7: Update the updated_at timestamp for migrated records
UPDATE public.volunteers 
SET updated_at = NOW() 
WHERE skills != '{}' OR languages_spoken != '{}' OR vector_metadata != '{}'::JSONB;

COMMIT;

-- Verification queries (run these after migration to verify)
/*
-- Check the migration results
SELECT 
    id,
    email,
    array_length(skills, 1) as skills_count,
    array_length(languages_spoken, 1) as languages_count,
    CASE WHEN vector_metadata = '{}'::JSONB THEN 'empty' ELSE 'has_data' END as vector_metadata_status
FROM public.volunteers 
LIMIT 10;

-- Check if any records have data in the new fields
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN array_length(skills, 1) > 0 THEN 1 END) as records_with_skills,
    COUNT(CASE WHEN array_length(languages_spoken, 1) > 0 THEN 1 END) as records_with_languages,
    COUNT(CASE WHEN vector_metadata != '{}'::JSONB THEN 1 END) as records_with_vector_metadata
FROM public.volunteers;
*/
