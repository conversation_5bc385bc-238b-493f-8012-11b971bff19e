"""
Pydantic models for the skill extractor backend.

This module defines all data models used throughout the application,
including LinkedIn profile structures, API requests/responses, and 
extracted skill/availability data.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, HttpUrl, EmailStr, validator

from common.logging import get_logger

logger = get_logger(__name__)


# === Enumerations ===

class WorkMode(str, Enum):
    """Enumeration of volunteer work availability modes."""
    FULL_TIME = "full_time"
    PART_TIME = "part_time"
    REMOTE = "remote"
    HYBRID = "hybrid"
    ON_SITE = "on_site"
    FLEXIBLE = "flexible"
    WEEKENDS_ONLY = "weekends_only"
    EVENINGS_ONLY = "evenings_only"


class SkillLevel(str, Enum):
    """Enumeration of skill proficiency levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


# === LinkedIn Profile Models ===

class LinkedInExperience(BaseModel):
    """Work experience entry from LinkedIn profile."""
    company_name: str = Field(..., description="Name of the company")
    job_title: str = Field(..., description="Job title or position")
    dates: str = Field(..., description="Employment date range")
    description: Optional[str] = Field(None, description="Job description")
    location: Optional[str] = Field(None, description="Work location")
    duration_months: Optional[int] = Field(None, description="Duration in months")


class LinkedInEducation(BaseModel):
    """Education entry from LinkedIn profile."""
    institution_name: str = Field(..., description="Educational institution name")
    degree: Optional[str] = Field(None, description="Degree type")
    field_of_study: Optional[str] = Field(None, description="Field of study")
    dates: Optional[str] = Field(None, description="Education date range")
    grade: Optional[str] = Field(None, description="Grade or GPA")
    activities: Optional[str] = Field(None, description="Activities and societies")


class LinkedInCertification(BaseModel):
    """Certification entry from LinkedIn profile."""
    name: str = Field(..., description="Certification name")
    issuing_organization: str = Field(..., description="Issuing organization")
    issue_date: Optional[str] = Field(None, description="Issue date")
    expiration_date: Optional[str] = Field(None, description="Expiration date")
    credential_id: Optional[str] = Field(None, description="Credential ID")
    credential_url: Optional[str] = Field(None, description="Credential URL")


class LinkedInVolunteerExperience(BaseModel):
    """Volunteer experience entry from LinkedIn profile."""
    organization: str = Field(..., description="Organization name")
    role: str = Field(..., description="Volunteer role")
    cause: Optional[str] = Field(None, description="Cause area")
    dates: Optional[str] = Field(None, description="Volunteer date range")
    description: Optional[str] = Field(None, description="Role description")
    duration_months: Optional[int] = Field(None, description="Duration in months")


class LinkedInProject(BaseModel):
    """Project entry from LinkedIn profile."""
    name: str = Field(..., description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    dates: Optional[str] = Field(None, description="Project date range")
    associated_with: Optional[str] = Field(None, description="Associated organization")
    url: Optional[str] = Field(None, description="Project URL")
    skills_used: List[str] = Field(default_factory=list, description="Skills used in project")


class LinkedInProfileData(BaseModel):
    """Complete LinkedIn profile data structure."""
    full_name: str = Field(..., description="Full name from profile")
    headline: Optional[str] = Field(None, description="Professional headline")
    location: Optional[str] = Field(None, description="Current location")
    about_summary: Optional[str] = Field(None, description="About section summary")
    work_experience: List[LinkedInExperience] = Field(default_factory=list)
    education: List[LinkedInEducation] = Field(default_factory=list)
    skills: List[str] = Field(default_factory=list, description="Listed skills")
    certifications: List[LinkedInCertification] = Field(default_factory=list)
    volunteer_experience: List[LinkedInVolunteerExperience] = Field(default_factory=list)
    projects: List[LinkedInProject] = Field(default_factory=list)
    languages: List[str] = Field(default_factory=list, description="Languages spoken")
    recommendations_received: int = Field(0, description="Number of recommendations")
    current_company: Optional[str] = Field(None, description="Current employer")
    current_position: Optional[str] = Field(None, description="Current job title")
    profile_url: Optional[str] = Field(None, description="LinkedIn profile URL")
    
    @validator('skills', 'languages')
    def clean_lists(cls, v):
        """Remove empty strings and duplicates from lists."""
        if v:
            return list(set(item.strip() for item in v if item.strip()))
        return []


# === API Request/Response Models ===

class LinkedInIngestRequest(BaseModel):
    """Request model for LinkedIn profile ingestion."""
    url: HttpUrl = Field(..., description="LinkedIn profile URL to process")
    email: Optional[EmailStr] = Field(None, description="Contact email if available")
    priority: int = Field(1, ge=1, le=5, description="Processing priority")
    
    @validator('url')
    def validate_linkedin_url(cls, v):
        """Ensure URL is a valid LinkedIn profile URL."""
        url_str = str(v)
        if not url_str.startswith('https://www.linkedin.com/in/'):
            raise ValueError('Must be a valid LinkedIn profile URL')
        return v


class ResumeIngestRequest(BaseModel):
    """Request model for resume PDF ingestion."""
    pdf_url: HttpUrl = Field(..., description="Signed S3 URL to resume PDF")
    email: Optional[EmailStr] = Field(None, description="Contact email if available")
    filename: Optional[str] = Field(None, description="Original filename")
    priority: int = Field(1, ge=1, le=5, description="Processing priority")


class IngestResponse(BaseModel):
    """Response model for ingestion requests."""
    volunteer_id: UUID = Field(..., description="Generated volunteer UUID")
    status: str = Field(..., description="Processing status")
    message: str = Field(..., description="Status message")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")


class DualStorageResult(BaseModel):
    """Result of dual storage operation."""
    volunteer_id: UUID = Field(..., description="Volunteer identifier")
    email: str = Field(..., description="Email address")
    supabase_success: bool = Field(..., description="Whether Supabase storage succeeded")
    qdrant_success: bool = Field(..., description="Whether Qdrant storage succeeded")
    skills_count: int = Field(0, description="Number of skills stored")
    vector_dimension: Optional[int] = Field(None, description="Vector dimension if stored")
    error_message: Optional[str] = Field(None, description="Error message if any operation failed")
    rollback_performed: bool = Field(False, description="Whether rollback was performed on failure")


# === Extracted Data Models ===

class ExtractedSkill(BaseModel):
    """Extracted skill with metadata."""
    name: str = Field(..., description="Skill name")
    level: Optional[SkillLevel] = Field(None, description="Proficiency level")
    years_experience: Optional[int] = Field(None, description="Years of experience")
    source: str = Field(..., description="Source of extraction (linkedin, resume, etc.)")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Extraction confidence")
    context: Optional[str] = Field(None, description="Context where skill was mentioned")


class QdrantMetadata(BaseModel):
    """Metadata for cross-referencing between Supabase and Qdrant (legacy single-vector)."""
    collection_name: str = Field(..., description="Qdrant collection name")
    point_id: str = Field(..., description="Qdrant point ID (volunteer_id)")
    vector_stored: bool = Field(False, description="Whether vector data is stored in Qdrant")
    last_sync: Optional[datetime] = Field(None, description="Last synchronization timestamp")
    vector_dimension: int = Field(384, description="Vector dimension size")


class MultiVectorMetadata(BaseModel):
    """Metadata for multi-vector Qdrant database references."""
    collection_name: str = Field(..., description="Name of the Qdrant collection")
    point_id: str = Field(..., description="Point ID in Qdrant collection")
    vector_fields: List[str] = Field(default_factory=list, description="List of vector field names stored")
    vector_dimension: int = Field(1536, description="Dimension of the stored vectors")
    vector_stored: bool = Field(False, description="Whether vector data is stored in Qdrant")
    last_sync: Optional[datetime] = Field(None, description="Last synchronization timestamp")
    created_at: datetime = Field(default_factory=datetime.utcnow)

    @validator('vector_fields')
    def validate_vector_fields(cls, v):
        """Validate vector field names."""
        expected_fields = {
            'skills_vector', 'certifications_vector', 'experience_vector',
            'summary_vector', 'languages_vector', 'volunteer_experience_vector', 'combined_vector'
        }

        if v:
            unexpected_fields = set(v) - expected_fields
            if unexpected_fields:
                # Just validate without logging to avoid import issues
                pass

        return v


class VolunteerRecord(BaseModel):
    """Volunteer record structure for Supabase storage with updated schema."""
    volunteer_id: UUID = Field(..., description="Volunteer identifier")
    email: EmailStr = Field(..., description="Email address (part of composite key)")
    skills: List[str] = Field(default_factory=list, description="Skills extracted from LinkedIn profiles")
    vector_metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadata for linking to Qdrant vector database records")
    languages_spoken: List[str] = Field(default_factory=list, description="Languages the volunteer speaks")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    @validator('skills', 'languages_spoken')
    def clean_string_lists(cls, v):
        """Remove empty strings and duplicates from string lists."""
        if v:
            return list(set(item.strip() for item in v if item.strip()))
        return []


class LinkedInProfileVector(BaseModel):
    """Complete LinkedIn profile data for Qdrant storage with multi-vector support."""
    volunteer_id: UUID = Field(..., description="Volunteer identifier (part of composite key)")
    email: EmailStr = Field(..., description="Email address (part of composite key)")
    profile_data: LinkedInProfileData = Field(..., description="Complete LinkedIn profile data")
    vectors: Dict[str, List[float]] = Field(default_factory=dict, description="Multi-vector embeddings for different profile sections")
    source_url: Optional[str] = Field(None, description="Original LinkedIn profile URL")
    extraction_timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata for vector search")

    @validator('vectors')
    def validate_vector_fields(cls, v):
        """Validate that vector fields match expected names and dimensions."""
        expected_fields = {
            'skills_vector', 'certifications_vector', 'experience_vector',
            'summary_vector', 'languages_vector', 'volunteer_experience_vector', 'combined_vector'
        }

        if v:
            # Check for unexpected vector fields (just validate, don't log)
            unexpected_fields = set(v.keys()) - expected_fields
            if unexpected_fields:
                # Just continue without logging to avoid import issues
                pass

            # Validate vector dimensions (should be 1536)
            for field_name, vector in v.items():
                if not isinstance(vector, list) or len(vector) != 1536:
                    raise ValueError(f"Vector field {field_name} must be a list of 1536 floats, got {type(vector)} with length {len(vector) if isinstance(vector, list) else 'N/A'}")

        return v


class ExtractedAvailability(BaseModel):
    """Extracted availability information."""
    work_modes: List[WorkMode] = Field(default_factory=list, description="Preferred work modes")
    hours_per_week: Optional[int] = Field(None, ge=1, le=168, description="Available hours per week")
    start_date: Optional[datetime] = Field(None, description="Earliest start date")
    duration_preference: Optional[str] = Field(None, description="Preferred engagement duration")
    location_preferences: List[str] = Field(default_factory=list, description="Location preferences")
    remote_willing: Optional[bool] = Field(None, description="Willing to work remotely")


class ExtractionResult(BaseModel):
    """Complete extraction result from LinkedIn or resume processing."""
    volunteer_id: UUID = Field(..., description="Volunteer identifier")
    source_type: str = Field(..., description="Source type (linkedin, resume)")
    source_url: Optional[str] = Field(None, description="Original source URL")
    
    # Personal Information
    full_name: Optional[str] = Field(None, description="Full name")
    email: Optional[EmailStr] = Field(None, description="Email address")
    location: Optional[str] = Field(None, description="Current location")
    
    # Professional Summary
    professional_summary: Optional[str] = Field(None, description="Professional summary")
    current_role: Optional[str] = Field(None, description="Current job title")
    current_company: Optional[str] = Field(None, description="Current employer")
    years_experience: Optional[int] = Field(None, description="Total years of experience")
    
    # Extracted Skills and Availability
    skills: List[ExtractedSkill] = Field(default_factory=list, description="Extracted skills")
    availability: Optional[ExtractedAvailability] = Field(None, description="Availability info")
    
    # Raw Data
    raw_text: str = Field(..., description="Raw extracted text")
    structured_data: Dict[str, Any] = Field(default_factory=dict, description="Structured source data")
    
    # Metadata
    extraction_timestamp: datetime = Field(default_factory=datetime.utcnow)
    extraction_version: str = Field("1.0", description="Extraction algorithm version")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Overall extraction confidence")


# === Query Models ===

class VectorSearchRequest(BaseModel):
    """Request model for multi-vector similarity search."""
    query_vector: Optional[List[float]] = Field(None, description="Query vector (legacy single vector)")
    query_text: Optional[str] = Field(None, description="Query text (will be vectorized)")
    volunteer_id: Optional[UUID] = Field(None, description="Reference volunteer ID")
    vector_field: str = Field("combined_vector", description="Which vector field to search against")
    top_k: int = Field(10, ge=1, le=100, description="Number of results to return")
    score_threshold: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum similarity score")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Metadata filters")

    @validator('vector_field')
    def validate_vector_field(cls, v):
        """Validate vector field name."""
        valid_fields = {
            'skills_vector', 'certifications_vector', 'experience_vector',
            'summary_vector', 'languages_vector', 'volunteer_experience_vector', 'combined_vector'
        }
        if v not in valid_fields:
            raise ValueError(f'vector_field must be one of: {valid_fields}')
        return v

    @validator('query_vector', 'query_text', 'volunteer_id')
    def validate_query_input(cls, v, values):
        """Ensure at least one query input is provided."""
        if not any([v, values.get('query_text'), values.get('volunteer_id')]):
            raise ValueError('Must provide query_vector, query_text, or volunteer_id')
        return v


class VectorSearchResult(BaseModel):
    """Individual search result."""
    volunteer_id: UUID = Field(..., description="Volunteer identifier")
    score: float = Field(..., description="Similarity score")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Volunteer metadata")


class VectorSearchResponse(BaseModel):
    """Response model for multi-vector similarity search."""
    results: List[VectorSearchResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    query_time_ms: float = Field(..., description="Query execution time in milliseconds")
    vector_field: str = Field(..., description="Vector field used for search")
    vector_dimension: int = Field(1536, description="Dimension of vectors used")


# === Health Check Models ===

class HealthStatus(str, Enum):
    """Health check status enumeration."""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"


class ComponentHealth(BaseModel):
    """Health status of a system component."""
    name: str = Field(..., description="Component name")
    status: HealthStatus = Field(..., description="Health status")
    message: Optional[str] = Field(None, description="Status message")
    response_time_ms: Optional[float] = Field(None, description="Response time")


class HealthCheckResponse(BaseModel):
    """Overall system health response."""
    status: HealthStatus = Field(..., description="Overall system status")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    components: List[ComponentHealth] = Field(..., description="Component health details")
    version: str = Field("1.0", description="API version") 