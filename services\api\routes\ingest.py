"""
Ingestion endpoints for the skill extractor API.

This module provides endpoints for submitting LinkedIn profiles and resume PDFs
for direct processing and extraction.
"""

import asyncio
from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, status
from fastapi.responses import JSONResponse

from common.models import (
    LinkedInIngestRequest,
    ResumeIngestRequest,
    IngestResponse,
    LinkedInProfileData,
    DualStorageResult
)
from common.settings import settings
from common.logging import get_logger
from common.utils import volunteer_id_from_email, generate_uuid
from services.api.middleware import get_current_user, require_role
from services.api.clients.browser_use import get_browser_use_client
from services.api.clients.database import get_database_client
from services.api.clients.qdrant import get_qdrant_client
from services.api.clients.dual_storage import get_dual_storage_service

logger = get_logger(__name__)

router = APIRouter()


def transform_linkedin_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform raw LinkedIn data from browser extraction to match our model structure.

    Args:
        raw_data: Raw data from browser extraction

    Returns:
        Transformed data matching LinkedInProfileData model
    """
    transformed = raw_data.copy()

    # Transform work experience - handle the actual structure from LinkedIn extraction
    if "work_experience" in transformed and isinstance(transformed["work_experience"], list):
        new_work_exp = []
        for exp in transformed["work_experience"]:
            # Handle the actual structure: company, title, date_range, etc.
            new_work_exp.append({
                "company_name": exp.get("company", ""),
                "job_title": exp.get("title", ""),
                "dates": exp.get("date_range", exp.get("dates", "")),
                "description": exp.get("description", ""),
                "location": exp.get("location", "")
            })
        transformed["work_experience"] = new_work_exp

    # Transform education - handle actual structure
    if "education" in transformed and isinstance(transformed["education"], list):
        new_education = []
        for edu in transformed["education"]:
            # Handle both 'institution' and 'institution_name' fields
            institution = edu.get("institution_name", edu.get("institution", ""))
            dates = edu.get("dates", "")
            # Handle start_year/end_year format
            if not dates and "start_year" in edu and "end_year" in edu:
                dates = f"{edu.get('start_year', '')} - {edu.get('end_year', '')}"

            new_education.append({
                "institution_name": institution,
                "degree": edu.get("degree", ""),
                "field_of_study": edu.get("field_of_study", ""),
                "dates": dates,
                "activities": edu.get("activities", "")
            })
        transformed["education"] = new_education

    # Transform certifications - handle actual structure
    if "certifications" in transformed and isinstance(transformed["certifications"], list):
        new_certs = []
        for cert in transformed["certifications"]:
            new_certs.append({
                "name": cert.get("name", ""),
                "issuing_organization": cert.get("issuer", cert.get("issuing_organization", "")),
                "issue_date": cert.get("issue_date", cert.get("date", "")),
                "credential_id": cert.get("credential_id", ""),
                "credential_url": cert.get("credential_url", "")
            })
        transformed["certifications"] = new_certs

    # Transform projects
    if "projects" in transformed and isinstance(transformed["projects"], list):
        new_projects = []
        for project in transformed["projects"]:
            new_projects.append({
                "name": project.get("title", ""),
                "description": project.get("description", ""),
                "associated_with": project.get("organization", ""),
                "dates": project.get("dates", ""),
                "skills_used": project.get("skills_used", [])
            })
        transformed["projects"] = new_projects

    # Transform volunteer experience - handle actual structure
    if "volunteer_experience" in transformed and isinstance(transformed["volunteer_experience"], list):
        new_volunteer = []
        for vol in transformed["volunteer_experience"]:
            # Handle both date formats
            dates = vol.get("dates", "")
            if not dates and "start_date" in vol and "end_date" in vol:
                dates = f"{vol.get('start_date', '')} - {vol.get('end_date', '')}"

            new_volunteer.append({
                "organization": vol.get("organization", ""),
                "role": vol.get("role", ""),
                "cause": vol.get("cause", ""),
                "dates": dates,
                "description": vol.get("description", "")
            })
        transformed["volunteer_experience"] = new_volunteer

    # Handle skills - ensure we extract them properly
    if "skills" in transformed:
        skills_data = transformed["skills"]
        if isinstance(skills_data, list):
            # Skills are already in list format
            transformed["skills"] = [skill for skill in skills_data if skill and skill.strip()]
        elif isinstance(skills_data, str):
            # Skills might be a comma-separated string
            transformed["skills"] = [skill.strip() for skill in skills_data.split(",") if skill.strip()]
        else:
            # Default to empty list
            transformed["skills"] = []
    else:
        transformed["skills"] = []

    return transformed


async def process_linkedin_profile(
    volunteer_id: UUID,
    linkedin_url: str,
    email: Optional[str] = None
) -> bool:
    """
    Background task to process LinkedIn profile extraction using dual storage.

    Args:
        volunteer_id: UUID of the volunteer
        linkedin_url: LinkedIn profile URL
        email: Optional email address

    Returns:
        True if processing was successful
    """
    try:
        logger.info(
            "Starting LinkedIn profile processing with dual storage",
            volunteer_id=str(volunteer_id),
            linkedin_url=linkedin_url
        )

        # Get browser client for extraction
        browser_client = get_browser_use_client()

        # Extract LinkedIn profile data
        raw_profile_data = await browser_client.extract_linkedin_profile(linkedin_url)

        if not raw_profile_data:
            logger.error(
                "LinkedIn profile extraction failed",
                volunteer_id=str(volunteer_id),
                linkedin_url=linkedin_url
            )
            return False

        # Transform and convert to LinkedInProfileData model
        try:
            # Transform the raw data to match our model structure
            transformed_data = transform_linkedin_data(raw_profile_data)
            profile_data = LinkedInProfileData(**transformed_data)
        except Exception as e:
            logger.error(
                f"Failed to parse LinkedIn profile data: {e}",
                volunteer_id=str(volunteer_id),
                raw_data_keys=list(raw_profile_data.keys()) if raw_profile_data else []
            )
            return False

        # Use email from profile if not provided
        final_email = email or profile_data.current_company or f"volunteer_{volunteer_id}@example.com"

        # Get dual storage service
        dual_storage = await get_dual_storage_service()

        # Store in both databases
        storage_result = await dual_storage.store_linkedin_profile(
            volunteer_id=volunteer_id,
            email=final_email,
            profile_data=profile_data,
            source_url=linkedin_url
        )

        # Check if storage was successful
        if storage_result.supabase_success and storage_result.qdrant_success:
            logger.info(
                "LinkedIn profile processing completed successfully",
                volunteer_id=str(volunteer_id),
                linkedin_url=linkedin_url,
                skills_count=storage_result.skills_count,
                vector_dimension=storage_result.vector_dimension
            )
            return True
        else:
            logger.error(
                "Dual storage failed",
                volunteer_id=str(volunteer_id),
                supabase_success=storage_result.supabase_success,
                qdrant_success=storage_result.qdrant_success,
                error_message=storage_result.error_message,
                rollback_performed=storage_result.rollback_performed
            )
            return False

    except Exception as e:
        logger.error(
            "LinkedIn profile processing failed",
            volunteer_id=str(volunteer_id),
            linkedin_url=linkedin_url,
            error=str(e),
            exc_info=True
        )
        return False


@router.post("/linkedin", response_model=IngestResponse)
async def ingest_linkedin_profile(
    request: LinkedInIngestRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(require_role("service_extract"))
):
    """
    Submit a LinkedIn profile URL for skill extraction.
    
    This endpoint:
    1. Validates the LinkedIn URL format
    2. Generates a deterministic volunteer ID from email (if provided)
    3. Starts background processing of the LinkedIn profile
    4. Returns the volunteer ID and processing status
    
    Requires 'service_extract' role for authentication.
    """
    
    logger.info(
        "LinkedIn ingestion request received",
        url=str(request.url),
        email=request.email,
        priority=request.priority,
        user_id=current_user.get("sub")
    )
    
    try:
        # Generate volunteer ID
        if request.email:
            volunteer_id = volunteer_id_from_email(request.email)
            logger.debug("Generated volunteer ID from email", volunteer_id=str(volunteer_id))
        else:
            volunteer_id = generate_uuid()
            logger.debug("Generated random volunteer ID", volunteer_id=str(volunteer_id))
        
        # Start background processing
        background_tasks.add_task(
            process_linkedin_profile,
            volunteer_id=volunteer_id,
            linkedin_url=str(request.url),
            email=request.email
        )
        
        # Log successful submission
        logger.info(
            "LinkedIn profile queued for processing",
            volunteer_id=str(volunteer_id),
            url=str(request.url),
            priority=request.priority
        )
        
        return IngestResponse(
            volunteer_id=volunteer_id,
            status="processing",
            message="LinkedIn profile submitted for processing",
            estimated_completion=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(
            "LinkedIn ingestion failed",
            url=str(request.url),
            error=str(e),
            user_id=current_user.get("sub"),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Ingestion failed",
                "message": "Failed to submit LinkedIn profile for processing",
                "type": "ingestion_error"
            }
        )


@router.post("/resume", response_model=IngestResponse)
async def ingest_resume_pdf(
    request: ResumeIngestRequest,
    current_user: dict = Depends(require_role("service_extract"))
):
    """
    Submit a resume PDF URL for skill extraction.
    
    This endpoint is a placeholder for future resume processing functionality.
    Currently returns a not implemented error.
    
    Requires 'service_extract' role for authentication.
    """
    
    logger.info(
        "Resume ingestion request received",
        pdf_url=str(request.pdf_url),
        filename=request.filename,
        email=request.email,
        priority=request.priority,
        user_id=current_user.get("sub")
    )
    
    # Resume processing not implemented yet
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail={
            "error": "Not implemented",
            "message": "Resume processing is not yet implemented",
            "type": "feature_not_implemented"
        }
    )


@router.get("/status/{volunteer_id}")
async def get_ingestion_status(
    volunteer_id: UUID,
    current_user: dict = Depends(get_current_user)
):
    """
    Get the processing status of a volunteer profile.
    
    This endpoint checks the database to see if a volunteer profile
    has been processed and returns the current status.
    """
    
    logger.info(
        "Ingestion status requested",
        volunteer_id=str(volunteer_id),
        user_id=current_user.get("sub")
    )
    
    try:
        # Get database client
        db_client = await get_database_client()
        
        # Check if volunteer exists in database
        volunteer_data = await db_client.get_volunteer_by_id(volunteer_id)
        
        if volunteer_data:
            return {
                "volunteer_id": volunteer_id,
                "status": "completed",
                "message": "Profile processing completed",
                "last_updated": volunteer_data.get("updated_at"),
                "source_types": volunteer_data.get("source_types", [])
            }
        else:
            return {
                "volunteer_id": volunteer_id,
                "status": "not_found",
                "message": "No processing record found for this volunteer ID"
            }
            
    except Exception as e:
        logger.error(
            "Failed to get ingestion status",
            volunteer_id=str(volunteer_id),
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Status check failed",
                "message": "Failed to retrieve processing status",
                "volunteer_id": str(volunteer_id)
            }
        )


@router.delete("/cancel/{volunteer_id}")
async def cancel_ingestion(
    volunteer_id: UUID,
    current_user: dict = Depends(require_role("admin"))
):
    """
    Cancel processing for a volunteer profile.
    
    Since processing is now direct (not queued), this endpoint
    can only delete existing records if needed.
    
    Requires 'admin' role for authentication.
    """
    
    logger.info(
        "Ingestion cancellation requested",
        volunteer_id=str(volunteer_id),
        user_id=current_user.get("sub")
    )
    
    try:
        # Get database client
        db_client = await get_database_client()
        
        # Check if volunteer exists
        volunteer_data = await db_client.get_volunteer_by_id(volunteer_id)
        
        if not volunteer_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "Volunteer not found",
                    "message": f"No volunteer found with ID {volunteer_id}",
                    "volunteer_id": str(volunteer_id)
                }
            )
        
        # Since we don't have queued processing, we can only provide status
        return {
            "volunteer_id": volunteer_id,
            "status": "completed",
            "message": "Profile processing has already completed",
            "note": "Direct processing cannot be cancelled once started"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to cancel ingestion",
            volunteer_id=str(volunteer_id),
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Cancellation failed",
                "message": "Failed to process cancellation request",
                "volunteer_id": str(volunteer_id)
            }
        )


@router.get("/stats")
async def get_processing_statistics(
    current_user: dict = Depends(require_role("admin"))
):
    """
    Get processing statistics and metrics.
    
    This endpoint provides information about the current state of
    volunteer profile processing.
    
    Requires 'admin' role for authentication.
    """
    
    logger.info(
        "Processing statistics requested",
        user_id=current_user.get("sub")
    )
    
    try:
        # Get database client
        db_client = await get_database_client()
        
        # Get database statistics
        db_stats = await db_client.get_statistics()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "processing_mode": "direct",
            "database_stats": db_stats,
            "message": "Statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(
            "Failed to get processing statistics",
            error=str(e),
            user_id=current_user.get("sub"),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Statistics unavailable",
                "message": "Failed to retrieve processing statistics",
                "type": "stats_error"
            }
        )


# Minimal LinkedIn endpoint without authentication (for testing)
@router.post("/linkedin-minimal", response_model=IngestResponse)
async def ingest_linkedin_profile_minimal(
    request: LinkedInIngestRequest,
    background_tasks: BackgroundTasks
):
    """
    Submit a LinkedIn profile URL for skill extraction (minimal version without auth).

    This endpoint:
    1. Validates the LinkedIn URL format
    2. Generates a deterministic volunteer ID from email (if provided)
    3. Starts background processing of the LinkedIn profile
    4. Returns the volunteer ID and processing status

    This is a minimal version without authentication for testing purposes.
    """

    logger.info(
        "LinkedIn ingestion request received (minimal)",
        url=str(request.url),
        email=request.email,
        priority=request.priority
    )

    try:
        # Generate volunteer ID
        if request.email:
            volunteer_id = volunteer_id_from_email(request.email)
            logger.debug("Generated volunteer ID from email", volunteer_id=str(volunteer_id))
        else:
            volunteer_id = generate_uuid()
            logger.debug("Generated random volunteer ID", volunteer_id=str(volunteer_id))

        # Start background processing
        background_tasks.add_task(
            process_linkedin_profile,
            volunteer_id=volunteer_id,
            linkedin_url=str(request.url),
            email=request.email
        )

        # Log successful submission
        logger.info(
            "LinkedIn profile queued for processing (minimal)",
            volunteer_id=str(volunteer_id),
            url=str(request.url),
            priority=request.priority
        )

        return IngestResponse(
            volunteer_id=volunteer_id,
            status="processing",
            message="LinkedIn profile submitted for processing (minimal mode)",
            estimated_completion=datetime.utcnow()
        )

    except Exception as e:
        logger.error(
            "LinkedIn ingestion failed (minimal)",
            url=str(request.url),
            error=str(e),
            exc_info=True
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Ingestion failed",
                "message": "Failed to submit LinkedIn profile for processing",
                "type": "ingestion_error"
            }
        )