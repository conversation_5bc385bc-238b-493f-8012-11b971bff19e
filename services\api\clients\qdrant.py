"""
Qdrant client for vector database operations.

This module provides functionality to interact with Qdrant vector database
for storing and searching volunteer profile embeddings.
"""

import asyncio
import os
from typing import List, Dict, Any, Optional, Union
from uuid import UUID

from qdrant_client import QdrantClient as QdrantClientCore
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue, NamedVector

from common.settings import settings
from common.logging import get_logger

logger = get_logger(__name__)


class QdrantClient:
    """
    Client for interacting with Qdrant vector database.

    This client provides methods for storing, retrieving, and searching
    volunteer profile embeddings in Qdrant with multi-vector support.
    """

    # Define the vector field names for multi-vector configuration
    VECTOR_FIELDS = {
        'skills_vector': 1536,
        'certifications_vector': 1536,
        'experience_vector': 1536,
        'summary_vector': 1536,
        'languages_vector': 1536,
        'volunteer_experience_vector': 1536,
        'combined_vector': 1536
    }

    def __init__(self):
        """Initialize the Qdrant client."""
        import os
        from dotenv import load_dotenv
        load_dotenv()

        self.client = None
        self.collection_name = os.getenv('QDRANT_COLLECTION', 'linkedin_profiles')
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize the Qdrant client connection."""
        try:
            import os
            from dotenv import load_dotenv
            load_dotenv()

            qdrant_url = os.getenv('QDRANT_URL', '')
            qdrant_api_key = os.getenv('QDRANT_API_KEY')

            # Use URL for cloud connections, fallback to host/port for local
            if qdrant_url.startswith(('http://', 'https://')):
                self.client = QdrantClientCore(
                    url=qdrant_url,
                    api_key=qdrant_api_key,
                    timeout=30.0
                )
                logger.info(
                    "Qdrant client initialized successfully with URL",
                    url=qdrant_url,
                    collection=self.collection_name
                )
            else:
                qdrant_host = os.getenv('QDRANT_HOST', 'localhost')
                qdrant_port = int(os.getenv('QDRANT_PORT', '6333'))
                qdrant_use_https = os.getenv('QDRANT_USE_HTTPS', 'false').lower() == 'true'

                self.client = QdrantClientCore(
                    host=qdrant_host,
                    port=qdrant_port,
                    api_key=qdrant_api_key,
                    https=qdrant_use_https,
                    timeout=30.0
                )
                logger.info(
                    "Qdrant client initialized successfully with host/port",
                    host=qdrant_host,
                    port=qdrant_port,
                    collection=self.collection_name
                )
            
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client: {e}")
            raise
    
    async def ensure_collection_exists(self) -> bool:
        """
        Ensure the collection exists with correct multi-vector configuration.
        If collection exists but has wrong configuration, recreate it.

        Returns:
            True if collection exists or was created successfully
        """
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if self.collection_name in collection_names:
                # Collection exists, check if it has correct multi-vector configuration
                try:
                    collection_info = self.client.get_collection(self.collection_name)

                    # Check if it's a multi-vector collection
                    if hasattr(collection_info.config.params, 'vectors') and isinstance(collection_info.config.params.vectors, dict):
                        # Multi-vector collection - check if it has all required fields
                        existing_vectors = set(collection_info.config.params.vectors.keys())
                        required_vectors = set(self.VECTOR_FIELDS.keys())

                        if existing_vectors == required_vectors:
                            logger.info(f"Collection '{self.collection_name}' already exists with correct multi-vector configuration")
                            return True
                        else:
                            logger.warning(f"Collection '{self.collection_name}' exists but has incorrect vector fields. Expected: {required_vectors}, Found: {existing_vectors}")
                    else:
                        logger.warning(f"Collection '{self.collection_name}' exists but is not a multi-vector collection")

                    # Delete and recreate collection with correct configuration
                    logger.info(f"Recreating collection '{self.collection_name}' with correct multi-vector configuration")
                    self.client.delete_collection(self.collection_name)

                except Exception as e:
                    logger.warning(f"Could not check collection configuration: {e}. Proceeding to recreate.")
                    try:
                        self.client.delete_collection(self.collection_name)
                    except:
                        pass  # Collection might not exist or be accessible

            # Create collection with multi-vector configuration
            logger.info(f"Creating collection '{self.collection_name}' with multi-vector support")

            # Build vectors config for multiple named vectors
            vectors_config = {}
            for vector_name, vector_size in self.VECTOR_FIELDS.items():
                vectors_config[vector_name] = VectorParams(
                    size=vector_size,
                    distance=Distance.COSINE
                )

            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=vectors_config
            )

            logger.info(
                f"Collection '{self.collection_name}' created successfully with vectors: {list(self.VECTOR_FIELDS.keys())}"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to ensure collection exists: {e}")
            return False
    
    async def upsert_point(
        self,
        volunteer_id: str,
        vectors: Dict[str, List[float]],
        metadata: Dict[str, Any]
    ) -> bool:
        """
        Upsert a multi-vector point for a volunteer.

        Args:
            volunteer_id: Unique identifier for the volunteer
            vectors: Dictionary of named vectors (e.g., {'skills_vector': [...], 'combined_vector': [...]})
            metadata: Additional metadata to store with the vector

        Returns:
            True if upsert was successful
        """
        try:
            # Ensure collection exists
            if not await self.ensure_collection_exists():
                return False

            # Validate vector fields
            for vector_name in vectors.keys():
                if vector_name not in self.VECTOR_FIELDS:
                    logger.warning(f"Unknown vector field: {vector_name}")

            # Create named vectors
            named_vectors = {}
            for vector_name, vector_data in vectors.items():
                if vector_name in self.VECTOR_FIELDS:
                    named_vectors[vector_name] = vector_data

            # Create point with named vectors
            point = PointStruct(
                id=volunteer_id,
                vector=named_vectors,
                payload=metadata
            )

            # Upsert point
            self.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )

            logger.info(
                f"Successfully upserted multi-vector point for volunteer {volunteer_id}",
                vector_fields=list(named_vectors.keys()),
                metadata_keys=list(metadata.keys())
            )

            return True

        except Exception as e:
            logger.error(
                f"Failed to upsert multi-vector point for volunteer {volunteer_id}: {e}",
                vector_fields=list(vectors.keys()) if vectors else []
            )
            return False

    async def upsert_point_legacy(
        self,
        volunteer_id: str,
        vector: List[float],
        metadata: Dict[str, Any]
    ) -> bool:
        """
        Legacy method for single vector upsert (for backward compatibility).
        Maps single vector to combined_vector field.

        Args:
            volunteer_id: Unique identifier for the volunteer
            vector: The embedding vector
            metadata: Additional metadata to store with the vector

        Returns:
            True if upsert was successful
        """
        return await self.upsert_point(
            volunteer_id=volunteer_id,
            vectors={'combined_vector': vector},
            metadata=metadata
        )
    
    async def get_point(self, volunteer_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a vector point by volunteer ID.

        Args:
            volunteer_id: Unique identifier for the volunteer

        Returns:
            Dictionary containing vector and metadata, or None if not found
        """
        try:
            # Get point
            points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=[volunteer_id],
                with_vectors=True,
                with_payload=True
            )

            if not points:
                logger.warning(f"No vector found for volunteer {volunteer_id}")
                return None

            point = points[0]

            # Handle both single vector and multi-vector formats
            result = {
                "id": str(point.id),
                "payload": point.payload or {}
            }

            # Check if it's multi-vector format
            if hasattr(point, 'vector') and isinstance(point.vector, dict):
                # Multi-vector format
                result["vectors"] = point.vector
            elif hasattr(point, 'vector') and point.vector is not None:
                # Legacy single vector format
                result["vector"] = point.vector

            return result

        except Exception as e:
            logger.error(f"Failed to retrieve vector for volunteer {volunteer_id}: {e}")
            return None

    async def get_point_by_composite_key(
        self,
        volunteer_id: str,
        email: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve a vector point by composite key (volunteer_id + email).

        Args:
            volunteer_id: Volunteer identifier
            email: Email address

        Returns:
            Dictionary containing vector and metadata, or None if not found
        """
        try:
            # First try to get by volunteer_id
            point_data = await self.get_point(volunteer_id)

            if point_data and point_data.get("payload", {}).get("email") == email:
                logger.debug(f"Found point with matching composite key: {volunteer_id}, {email}")
                return point_data

            # If not found or email doesn't match, search by email
            search_results = await self.search_by_metadata({"email": email})

            for result in search_results:
                if result.get("payload", {}).get("volunteer_id") == volunteer_id:
                    logger.debug(f"Found point via email search with matching volunteer_id")
                    return result

            logger.warning(f"No vector found for composite key: {volunteer_id}, {email}")
            return None

        except Exception as e:
            logger.error(f"Failed to retrieve vector by composite key {volunteer_id}, {email}: {e}")
            return None

    async def search_by_metadata(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Search points by metadata filters.

        Args:
            filters: Dictionary of metadata filters

        Returns:
            List of matching points with metadata
        """
        try:
            # Build filter conditions
            conditions = []
            for key, value in filters.items():
                conditions.append(
                    FieldCondition(
                        key=key,
                        match=MatchValue(value=value)
                    )
                )

            if not conditions:
                return []

            qdrant_filter = Filter(must=conditions)

            # Perform search with a dummy vector (we're only interested in metadata)
            # Use a zero vector for metadata-only search with combined_vector field
            dummy_vector = [0.0] * self.VECTOR_FIELDS['combined_vector']

            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=NamedVector(
                    name="combined_vector",
                    vector=dummy_vector
                ),
                limit=100,  # Reasonable limit for metadata search
                query_filter=qdrant_filter,
                with_payload=True,
                with_vectors=True
            )

            # Convert results
            results = []
            for hit in search_result:
                results.append({
                    "id": str(hit.id),
                    "score": hit.score,
                    "vector": hit.vector,
                    "payload": hit.payload or {}
                })

            logger.debug(f"Metadata search returned {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"Failed to search by metadata: {e}")
            return []
    
    async def search_similar(
        self,
        query_vector: List[float],
        vector_field: str = "combined_vector",
        top_k: int = 10,
        score_threshold: Optional[float] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar vectors using a specific vector field.

        Args:
            query_vector: The query vector to search with
            vector_field: Which vector field to search against (default: combined_vector)
            top_k: Number of results to return
            score_threshold: Minimum similarity score
            filters: Additional filters to apply

        Returns:
            List of similar vectors with scores and metadata
        """
        try:
            # Ensure collection exists
            if not await self.ensure_collection_exists():
                return []

            # Validate vector field
            if vector_field not in self.VECTOR_FIELDS:
                logger.error(f"Invalid vector field: {vector_field}")
                return []

            # Build filter if provided
            qdrant_filter = None
            if filters:
                conditions = []
                for key, value in filters.items():
                    conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value)
                        )
                    )
                if conditions:
                    qdrant_filter = Filter(must=conditions)

            # Perform search with named vector
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=NamedVector(
                    name=vector_field,
                    vector=query_vector
                ),
                limit=top_k,
                score_threshold=score_threshold,
                query_filter=qdrant_filter,
                with_payload=True,
                with_vectors=True
            )

            # Convert results
            results = []
            for hit in search_result:
                results.append({
                    "id": str(hit.id),
                    "score": hit.score,
                    "vectors": hit.vector if hasattr(hit, 'vector') else None,
                    "payload": hit.payload or {}
                })

            logger.info(
                f"Multi-vector search completed",
                vector_field=vector_field,
                results_count=len(results),
                top_k=top_k,
                score_threshold=score_threshold
            )

            return results

        except Exception as e:
            logger.error(f"Failed to search similar vectors: {e}")
            return []

    async def search_similar_legacy(
        self,
        query_vector: List[float],
        top_k: int = 10,
        score_threshold: Optional[float] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Legacy search method for backward compatibility.
        Uses combined_vector field by default.
        """
        return await self.search_similar(
            query_vector=query_vector,
            vector_field="combined_vector",
            top_k=top_k,
            score_threshold=score_threshold,
            filters=filters
        )
    
    async def delete_point(self, volunteer_id: str) -> bool:
        """
        Delete a vector point by volunteer ID.
        
        Args:
            volunteer_id: Unique identifier for the volunteer
            
        Returns:
            True if deletion was successful
        """
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=[volunteer_id]
                )
            )
            
            logger.info(f"Successfully deleted vector for volunteer {volunteer_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vector for volunteer {volunteer_id}: {e}")
            return False
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """
        Get information about the collection.
        
        Returns:
            Dictionary containing collection statistics
        """
        try:
            collection_info = self.client.get_collection(self.collection_name)
            
            # Build response with available attributes
            response = {
                "name": self.collection_name,
                "vectors_count": getattr(collection_info, 'vectors_count', 0),
                "points_count": getattr(collection_info, 'points_count', 0),
                "segments_count": getattr(collection_info, 'segments_count', 0),
            }

            # Add optional attributes if they exist
            if hasattr(collection_info, 'disk_data_size'):
                response["disk_data_size"] = collection_info.disk_data_size
            if hasattr(collection_info, 'ram_data_size'):
                response["ram_data_size"] = collection_info.ram_data_size

            # Add config if available
            try:
                # Handle multi-vector configuration
                if hasattr(collection_info.config.params, 'vectors') and isinstance(collection_info.config.params.vectors, dict):
                    # Multi-vector configuration
                    vectors_config = {}
                    for vector_name, vector_config in collection_info.config.params.vectors.items():
                        vectors_config[vector_name] = {
                            "size": vector_config.size,
                            "distance": vector_config.distance.value
                        }
                    response["config"] = {
                        "type": "multi_vector",
                        "vectors": vectors_config
                    }
                else:
                    # Legacy single vector configuration
                    response["config"] = {
                        "type": "single_vector",
                        "vector_size": collection_info.config.params.vectors.size,
                        "distance": collection_info.config.params.vectors.distance.value
                    }
            except AttributeError:
                response["config"] = {"status": "config_unavailable"}

            return response
            
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {
                "name": self.collection_name,
                "error": str(e),
                "status": "unavailable"
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the Qdrant client.
        
        Returns:
            Dictionary containing health check results
        """
        try:
            # Test basic connectivity
            collections = self.client.get_collections()
            
            # Check if our collection exists
            collection_names = [col.name for col in collections.collections]
            collection_exists = self.collection_name in collection_names
            
            # Get collection info if it exists
            collection_info = None
            if collection_exists:
                collection_info = await self.get_collection_info()
            
            return {
                "status": "healthy",
                "connected": True,
                "collection_exists": collection_exists,
                "collection_info": collection_info,
                "total_collections": len(collections.collections)
            }
            
        except Exception as e:
            logger.error(f"Qdrant health check failed: {e}")
            return {
                "status": "unhealthy",
                "connected": False,
                "error": str(e)
            }


# Global client instance
_qdrant_client = None


def get_qdrant_client() -> QdrantClient:
    """
    Get the global Qdrant client instance.
    
    Returns:
        QdrantClient instance
    """
    global _qdrant_client
    
    if _qdrant_client is None:
        _qdrant_client = QdrantClient()
    
    return _qdrant_client


async def qdrant_health_check() -> Dict[str, Any]:
    """
    Convenience function to perform a Qdrant health check.
    
    Returns:
        Dictionary containing health check results
    """
    client = get_qdrant_client()
    return await client.health_check() 