"""
Vector generation service for LinkedIn profile data.

This module provides functionality to generate embeddings from LinkedIn profile data
using sentence transformers for storage in Qdrant vector database.
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime

from sentence_transformers import SentenceTransformer

from common.models import LinkedInProfileData
from common.settings import settings
from common.logging import get_logger

logger = get_logger(__name__)


class VectorGenerationService:
    """
    Service for generating vector embeddings from LinkedIn profile data.

    This service uses sentence transformers to create embeddings that can be
    used for semantic similarity search in Qdrant. Supports multi-vector generation
    for different profile sections.
    """

    # Vector field names matching Qdrant configuration
    VECTOR_FIELDS = [
        'skills_vector',
        'certifications_vector',
        'experience_vector',
        'summary_vector',
        'languages_vector',
        'volunteer_experience_vector',
        'combined_vector'
    ]

    def __init__(self):
        """Initialize the vector generation service."""
        self._sentence_transformer = None
        self._initialized = False
        # Use a model that generates 1536-dimensional vectors (e.g., text-embedding-ada-002 compatible)
        # For now, we'll use all-MiniLM-L6-v2 and pad/truncate to 1536 dimensions
        self._model_name = settings.workers.sentence_transformer_model
        self._target_dimension = 1536
    
    async def initialize(self):
        """Initialize the service and load the sentence transformer model."""
        if self._initialized:
            return
        
        try:
            logger.info(f"Loading sentence transformer model: {self._model_name}")
            
            # Load model in a thread to avoid blocking
            loop = asyncio.get_event_loop()
            self._sentence_transformer = await loop.run_in_executor(
                None, 
                lambda: SentenceTransformer(self._model_name)
            )
            
            self._initialized = True
            logger.info(
                "Vector generation service initialized successfully",
                model_name=self._model_name,
                vector_dimension=self._sentence_transformer.get_sentence_embedding_dimension()
            )
            
        except Exception as e:
            logger.error(f"Failed to initialize vector generation service: {e}")
            raise
    
    def _create_profile_text(self, profile_data: LinkedInProfileData) -> str:
        """
        Create a comprehensive text representation of the LinkedIn profile.
        
        Args:
            profile_data: Complete LinkedIn profile data
            
        Returns:
            Combined text representation for embedding generation
        """
        text_parts = []
        
        # Add basic information
        if profile_data.full_name:
            text_parts.append(f"Name: {profile_data.full_name}")
        
        if profile_data.headline:
            text_parts.append(f"Professional Headline: {profile_data.headline}")
        
        if profile_data.location:
            text_parts.append(f"Location: {profile_data.location}")
        
        if profile_data.about_summary:
            text_parts.append(f"About: {profile_data.about_summary}")
        
        # Add current role information
        if profile_data.current_company and profile_data.current_position:
            text_parts.append(f"Current Role: {profile_data.current_position} at {profile_data.current_company}")
        elif profile_data.current_company:
            text_parts.append(f"Current Company: {profile_data.current_company}")
        elif profile_data.current_position:
            text_parts.append(f"Current Position: {profile_data.current_position}")
        
        # Add skills
        if profile_data.skills:
            skills_text = ", ".join(profile_data.skills)
            text_parts.append(f"Skills: {skills_text}")
        
        # Add work experience
        for exp in profile_data.work_experience:
            exp_parts = []
            exp_parts.append(f"{exp.job_title} at {exp.company_name}")
            
            if exp.dates:
                exp_parts.append(f"({exp.dates})")
            
            if exp.location:
                exp_parts.append(f"in {exp.location}")
            
            if exp.description:
                exp_parts.append(f"- {exp.description}")
            
            text_parts.append(f"Experience: {' '.join(exp_parts)}")
        
        # Add education
        for edu in profile_data.education:
            edu_parts = []
            
            if edu.degree and edu.field_of_study:
                edu_parts.append(f"{edu.degree} in {edu.field_of_study}")
            elif edu.degree:
                edu_parts.append(edu.degree)
            elif edu.field_of_study:
                edu_parts.append(edu.field_of_study)
            
            edu_parts.append(f"from {edu.institution_name}")
            
            if edu.dates:
                edu_parts.append(f"({edu.dates})")
            
            if edu.activities:
                edu_parts.append(f"Activities: {edu.activities}")
            
            text_parts.append(f"Education: {' '.join(edu_parts)}")
        
        # Add certifications
        for cert in profile_data.certifications:
            cert_parts = [cert.name, f"by {cert.issuing_organization}"]
            
            if cert.issue_date:
                cert_parts.append(f"issued {cert.issue_date}")
            
            text_parts.append(f"Certification: {' '.join(cert_parts)}")
        
        # Add volunteer experience
        for vol in profile_data.volunteer_experience:
            vol_parts = [f"{vol.role} at {vol.organization}"]
            
            if vol.cause:
                vol_parts.append(f"for {vol.cause}")
            
            if vol.dates:
                vol_parts.append(f"({vol.dates})")
            
            if vol.description:
                vol_parts.append(f"- {vol.description}")
            
            text_parts.append(f"Volunteer: {' '.join(vol_parts)}")
        
        # Add projects
        for project in profile_data.projects:
            project_parts = [project.name]
            
            if project.associated_with:
                project_parts.append(f"at {project.associated_with}")
            
            if project.dates:
                project_parts.append(f"({project.dates})")
            
            if project.description:
                project_parts.append(f"- {project.description}")
            
            if project.skills_used:
                skills_text = ", ".join(project.skills_used)
                project_parts.append(f"Skills used: {skills_text}")
            
            text_parts.append(f"Project: {' '.join(project_parts)}")
        
        # Add languages
        if profile_data.languages:
            languages_text = ", ".join(profile_data.languages)
            text_parts.append(f"Languages: {languages_text}")
        
        # Combine all parts with separators
        combined_text = " | ".join(text_parts)
        
        logger.debug(
            f"Created profile text representation",
            text_length=len(combined_text),
            sections_count=len(text_parts)
        )
        
        return combined_text

    def _create_skills_text(self, profile_data: LinkedInProfileData) -> str:
        """Create text representation focused on skills."""
        text_parts = []

        # Add skills
        if profile_data.skills:
            skills_text = ", ".join(profile_data.skills)
            text_parts.append(f"Skills: {skills_text}")

        # Add skills from projects
        for project in profile_data.projects:
            if project.skills_used:
                skills_text = ", ".join(project.skills_used)
                text_parts.append(f"Project skills: {skills_text}")

        return " | ".join(text_parts) if text_parts else "No skills listed"

    def _create_certifications_text(self, profile_data: LinkedInProfileData) -> str:
        """Create text representation focused on certifications."""
        text_parts = []

        for cert in profile_data.certifications:
            cert_parts = [cert.name, f"by {cert.issuing_organization}"]

            if cert.issue_date:
                cert_parts.append(f"issued {cert.issue_date}")

            text_parts.append(" ".join(cert_parts))

        return " | ".join(text_parts) if text_parts else "No certifications listed"

    def _create_experience_text(self, profile_data: LinkedInProfileData) -> str:
        """Create text representation focused on work experience."""
        text_parts = []

        # Add current role information
        if profile_data.current_company and profile_data.current_position:
            text_parts.append(f"Current: {profile_data.current_position} at {profile_data.current_company}")

        # Add work experience
        for exp in profile_data.work_experience:
            exp_parts = []
            exp_parts.append(f"{exp.job_title} at {exp.company_name}")

            if exp.dates:
                exp_parts.append(f"({exp.dates})")

            if exp.description:
                exp_parts.append(f"- {exp.description}")

            text_parts.append(" ".join(exp_parts))

        return " | ".join(text_parts) if text_parts else "No work experience listed"

    def _create_summary_text(self, profile_data: LinkedInProfileData) -> str:
        """Create text representation focused on summary and basic info."""
        text_parts = []

        # Add basic information
        if profile_data.full_name:
            text_parts.append(f"Name: {profile_data.full_name}")

        if profile_data.headline:
            text_parts.append(f"Headline: {profile_data.headline}")

        if profile_data.location:
            text_parts.append(f"Location: {profile_data.location}")

        if profile_data.about_summary:
            text_parts.append(f"About: {profile_data.about_summary}")

        return " | ".join(text_parts) if text_parts else "No summary information"

    def _create_languages_text(self, profile_data: LinkedInProfileData) -> str:
        """Create text representation focused on languages."""
        if profile_data.languages:
            languages_text = ", ".join(profile_data.languages)
            return f"Languages: {languages_text}"
        return "No languages listed"

    def _create_volunteer_experience_text(self, profile_data: LinkedInProfileData) -> str:
        """Create text representation focused on volunteer experience."""
        text_parts = []

        for vol in profile_data.volunteer_experience:
            vol_parts = [f"{vol.role} at {vol.organization}"]

            if vol.cause:
                vol_parts.append(f"for {vol.cause}")

            if vol.dates:
                vol_parts.append(f"({vol.dates})")

            if vol.description:
                vol_parts.append(f"- {vol.description}")

            text_parts.append(" ".join(vol_parts))

        return " | ".join(text_parts) if text_parts else "No volunteer experience listed"

    def _normalize_vector_dimension(self, vector: List[float]) -> List[float]:
        """
        Normalize vector to target dimension (1536).

        Args:
            vector: Input vector of any dimension

        Returns:
            Vector normalized to target dimension
        """
        current_dim = len(vector)

        if current_dim == self._target_dimension:
            return vector
        elif current_dim < self._target_dimension:
            # Pad with zeros
            return vector + [0.0] * (self._target_dimension - current_dim)
        else:
            # Truncate
            return vector[:self._target_dimension]

    async def generate_embedding(self, profile_data: LinkedInProfileData) -> List[float]:
        """
        Generate combined vector embedding from LinkedIn profile data.

        Args:
            profile_data: Complete LinkedIn profile data

        Returns:
            Vector embedding as list of floats (normalized to target dimension)
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Create text representation
            profile_text = self._create_profile_text(profile_data)

            # Generate embedding
            loop = asyncio.get_event_loop()
            vector = await loop.run_in_executor(
                None,
                lambda: self._sentence_transformer.encode(profile_text).tolist()
            )

            # Normalize to target dimension
            normalized_vector = self._normalize_vector_dimension(vector)

            logger.debug(
                f"Generated combined vector embedding",
                original_dimension=len(vector),
                normalized_dimension=len(normalized_vector),
                text_length=len(profile_text)
            )

            return normalized_vector

        except Exception as e:
            logger.error(f"Failed to generate vector embedding: {e}")
            raise

    async def generate_multi_vector_embeddings(self, profile_data: LinkedInProfileData) -> Dict[str, List[float]]:
        """
        Generate multiple vector embeddings for different profile sections.

        Args:
            profile_data: Complete LinkedIn profile data

        Returns:
            Dictionary mapping vector field names to their embeddings
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Create text representations for each vector type
            text_representations = {
                'skills_vector': self._create_skills_text(profile_data),
                'certifications_vector': self._create_certifications_text(profile_data),
                'experience_vector': self._create_experience_text(profile_data),
                'summary_vector': self._create_summary_text(profile_data),
                'languages_vector': self._create_languages_text(profile_data),
                'volunteer_experience_vector': self._create_volunteer_experience_text(profile_data),
                'combined_vector': self._create_profile_text(profile_data)
            }

            # Generate embeddings for all text representations
            all_texts = list(text_representations.values())

            loop = asyncio.get_event_loop()
            all_vectors = await loop.run_in_executor(
                None,
                lambda: self._sentence_transformer.encode(all_texts).tolist()
            )

            # Map vectors back to field names and normalize dimensions
            result = {}
            for i, (field_name, _) in enumerate(text_representations.items()):
                normalized_vector = self._normalize_vector_dimension(all_vectors[i])
                result[field_name] = normalized_vector

            logger.info(
                f"Generated multi-vector embeddings",
                vector_fields=list(result.keys()),
                vector_dimension=self._target_dimension
            )

            return result

        except Exception as e:
            logger.error(f"Failed to generate multi-vector embeddings: {e}")
            raise
    
    async def generate_embeddings_batch(
        self,
        profiles: List[LinkedInProfileData]
    ) -> List[List[float]]:
        """
        Generate combined vector embeddings for multiple profiles in batch.

        Args:
            profiles: List of LinkedIn profile data

        Returns:
            List of vector embeddings (normalized to target dimension)
        """
        if not self._initialized:
            await self.initialize()

        try:
            # Create text representations for all profiles
            profile_texts = [self._create_profile_text(profile) for profile in profiles]

            # Generate embeddings in batch
            loop = asyncio.get_event_loop()
            vectors = await loop.run_in_executor(
                None,
                lambda: self._sentence_transformer.encode(profile_texts).tolist()
            )

            # Normalize all vectors to target dimension
            normalized_vectors = [self._normalize_vector_dimension(vector) for vector in vectors]

            logger.info(
                f"Generated batch embeddings",
                batch_size=len(profiles),
                original_dimension=len(vectors[0]) if vectors else 0,
                normalized_dimension=self._target_dimension
            )

            return normalized_vectors

        except Exception as e:
            logger.error(f"Failed to generate batch embeddings: {e}")
            raise

    async def generate_multi_vector_embeddings_batch(
        self,
        profiles: List[LinkedInProfileData]
    ) -> List[Dict[str, List[float]]]:
        """
        Generate multi-vector embeddings for multiple profiles in batch.

        Args:
            profiles: List of LinkedIn profile data

        Returns:
            List of dictionaries mapping vector field names to their embeddings
        """
        if not self._initialized:
            await self.initialize()

        try:
            results = []

            # Process each profile individually for now
            # TODO: Optimize for true batch processing
            for profile in profiles:
                multi_vectors = await self.generate_multi_vector_embeddings(profile)
                results.append(multi_vectors)

            logger.info(
                f"Generated multi-vector batch embeddings",
                batch_size=len(profiles),
                vector_fields=list(results[0].keys()) if results else [],
                vector_dimension=self._target_dimension
            )

            return results

        except Exception as e:
            logger.error(f"Failed to generate multi-vector batch embeddings: {e}")
            raise
    
    def get_vector_dimension(self) -> int:
        """
        Get the dimension of vectors produced by this service.
        
        Returns:
            Vector dimension
        """
        if self._sentence_transformer:
            return self._sentence_transformer.get_sentence_embedding_dimension()
        return settings.qdrant.vector_size
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the vector generation service.
        
        Returns:
            Dictionary containing health check results
        """
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test embedding generation with a simple text
            test_text = "Test profile for health check"
            loop = asyncio.get_event_loop()
            test_vector = await loop.run_in_executor(
                None,
                lambda: self._sentence_transformer.encode(test_text).tolist()
            )
            
            return {
                "status": "healthy",
                "model_name": self._model_name,
                "vector_dimension": len(test_vector),
                "initialized": self._initialized,
                "test_embedding_generated": True
            }
            
        except Exception as e:
            logger.error(f"Vector generation service health check failed: {e}")
            return {
                "status": "unhealthy",
                "model_name": self._model_name,
                "initialized": self._initialized,
                "error": str(e)
            }


# Global service instance
_vector_service = None


async def get_vector_service() -> VectorGenerationService:
    """
    Get the global vector generation service instance.
    
    Returns:
        VectorGenerationService instance
    """
    global _vector_service
    
    if _vector_service is None:
        _vector_service = VectorGenerationService()
        await _vector_service.initialize()
    
    return _vector_service
