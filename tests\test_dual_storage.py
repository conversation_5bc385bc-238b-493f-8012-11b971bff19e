"""
Test suite for dual storage implementation.

This module tests the dual storage architecture that coordinates between
Supabase and Qdrant for storing LinkedIn profile data.
"""

import pytest
import asyncio
from uuid import uuid4
from datetime import datetime

from common.models import LinkedInProfileData, QdrantMetadata, MultiVectorMetadata, DualStorageResult
from services.api.clients.dual_storage import get_dual_storage_service
from services.api.clients.database import get_database_client
from services.api.clients.qdrant import get_qdrant_client
from services.api.clients.vector_service import get_vector_service


@pytest.fixture
def sample_linkedin_profile():
    """Create a sample LinkedIn profile for testing."""
    return LinkedInProfileData(
        full_name="<PERSON>",
        headline="Software Engineer at Tech Corp",
        location="San Francisco, CA",
        about_summary="Experienced software engineer with expertise in Python and machine learning.",
        skills=["Python", "Machine Learning", "FastAPI", "PostgreSQL", "Docker"],
        languages=["English", "Spanish"],
        current_company="Tech Corp",
        current_position="Senior Software Engineer",
        work_experience=[
            {
                "company_name": "Tech Corp",
                "job_title": "Senior Software Engineer",
                "dates": "2022 - Present",
                "description": "Leading development of ML-powered applications",
                "location": "San Francisco, CA"
            }
        ],
        education=[
            {
                "institution_name": "University of California",
                "degree": "Bachelor of Science",
                "field_of_study": "Computer Science",
                "dates": "2018 - 2022"
            }
        ],
        certifications=[
            {
                "name": "AWS Certified Solutions Architect",
                "issuing_organization": "Amazon Web Services",
                "issue_date": "2023"
            }
        ]
    )


@pytest.mark.asyncio
async def test_vector_service_initialization():
    """Test that the vector generation service initializes correctly."""
    vector_service = await get_vector_service()
    
    # Test health check
    health_result = await vector_service.health_check()
    assert health_result["status"] == "healthy"
    assert "model_name" in health_result
    assert "vector_dimension" in health_result
    assert health_result["initialized"] is True


@pytest.mark.asyncio
async def test_vector_generation(sample_linkedin_profile):
    """Test single vector generation from LinkedIn profile data."""
    vector_service = await get_vector_service()

    # Generate combined vector
    vector = await vector_service.generate_embedding(sample_linkedin_profile)

    # Verify vector properties
    assert isinstance(vector, list)
    assert len(vector) == 1536  # Expected dimension for multi-vector architecture
    assert all(isinstance(x, float) for x in vector)

    # Vector should not be all zeros
    assert any(x != 0.0 for x in vector)


@pytest.mark.asyncio
async def test_multi_vector_generation(sample_linkedin_profile):
    """Test multi-vector generation from LinkedIn profile data."""
    vector_service = await get_vector_service()

    # Generate multi-vectors
    vectors = await vector_service.generate_multi_vector_embeddings(sample_linkedin_profile)

    # Verify multi-vector properties
    assert isinstance(vectors, dict)

    # Check expected vector fields
    expected_fields = {
        'skills_vector', 'certifications_vector', 'experience_vector',
        'summary_vector', 'languages_vector', 'volunteer_experience_vector', 'combined_vector'
    }
    assert set(vectors.keys()) == expected_fields

    # Verify each vector
    for field_name, vector in vectors.items():
        assert isinstance(vector, list)
        assert len(vector) == 1536  # Expected dimension
        assert all(isinstance(x, float) for x in vector)
        # Vector should not be all zeros
        assert any(x != 0.0 for x in vector)


@pytest.mark.asyncio
async def test_dual_storage_service_initialization():
    """Test that the dual storage service initializes correctly."""
    dual_storage = await get_dual_storage_service()
    
    # Service should be initialized
    assert dual_storage._initialized is True
    assert dual_storage._vector_service is not None


@pytest.mark.asyncio
async def test_dual_storage_linkedin_profile(sample_linkedin_profile):
    """Test storing LinkedIn profile data in both databases."""
    dual_storage = await get_dual_storage_service()

    # Generate test data with unique email
    volunteer_id = uuid4()
    email = f"john.doe.{str(volunteer_id)[:8]}@example.com"
    source_url = "https://www.linkedin.com/in/johndoe/"
    
    # Store profile
    result = await dual_storage.store_linkedin_profile(
        volunteer_id=volunteer_id,
        email=email,
        profile_data=sample_linkedin_profile,
        source_url=source_url
    )
    
    # Verify storage result
    assert isinstance(result, DualStorageResult)
    assert result.volunteer_id == volunteer_id
    assert result.email == email
    assert result.supabase_success is True
    assert result.qdrant_success is True
    assert result.skills_count > 0
    assert result.vector_dimension == 1536  # Multi-vector dimension
    assert result.error_message is None
    assert result.rollback_performed is False
    
    # Clean up - remove test data
    try:
        # Remove from Qdrant
        qdrant_client = get_qdrant_client()
        await qdrant_client.delete_point(str(volunteer_id))
        
        # Note: In a real test environment, you might want to clean up Supabase too
        # but be careful not to delete production data
    except Exception as e:
        print(f"Cleanup warning: {e}")


@pytest.mark.asyncio
async def test_cross_database_lookup(sample_linkedin_profile):
    """Test cross-database lookup functionality."""
    dual_storage = await get_dual_storage_service()
    db_client = await get_database_client()
    qdrant_client = get_qdrant_client()
    
    # Generate test data with unique email
    volunteer_id = uuid4()
    email = f"jane.doe.{str(volunteer_id)[:8]}@example.com"
    
    # Store profile
    result = await dual_storage.store_linkedin_profile(
        volunteer_id=volunteer_id,
        email=email,
        profile_data=sample_linkedin_profile,
        source_url="https://www.linkedin.com/in/janedoe/"
    )
    
    assert result.supabase_success and result.qdrant_success
    
    # Test Supabase lookup
    volunteer_data = await db_client.get_volunteer_by_id(volunteer_id)
    assert volunteer_data is not None
    assert volunteer_data["email"] == email

    # Check that volunteer data has new schema fields
    assert "skills" in volunteer_data
    assert "vector_metadata" in volunteer_data
    assert "languages_spoken" in volunteer_data
    assert isinstance(volunteer_data["skills"], list)
    assert isinstance(volunteer_data["vector_metadata"], dict)
    assert isinstance(volunteer_data["languages_spoken"], list)
    
    # Test Qdrant lookup
    vector_data = await qdrant_client.get_point(str(volunteer_id))
    assert vector_data is not None
    assert "vectors" in vector_data or "vector" in vector_data  # Support both formats
    assert "payload" in vector_data
    assert vector_data["payload"]["email"] == email
    
    # Test cross-database lookup
    combined_data = await db_client.get_volunteer_with_qdrant_lookup(
        volunteer_id=volunteer_id,
        include_vector_data=True
    )
    assert combined_data is not None
    assert "vector_data" in combined_data
    assert combined_data["vector_data"]["payload"]["email"] == email
    
    # Clean up
    try:
        await qdrant_client.delete_point(str(volunteer_id))
    except Exception as e:
        print(f"Cleanup warning: {e}")


@pytest.mark.asyncio
async def test_dual_storage_rollback():
    """Test rollback functionality when one database fails."""
    # This test would require mocking one of the database clients to fail
    # For now, we'll test the basic rollback logic structure
    
    dual_storage = await get_dual_storage_service()
    
    # Test with invalid data that should cause Supabase to fail
    volunteer_id = uuid4()
    email = "invalid-email"  # This might cause validation issues
    
    # Create minimal profile data
    minimal_profile = LinkedInProfileData(
        full_name="Test User",
        skills=["Testing"]
    )
    
    result = await dual_storage.store_linkedin_profile(
        volunteer_id=volunteer_id,
        email=email,
        profile_data=minimal_profile
    )
    
    # The result should indicate what happened
    assert isinstance(result, DualStorageResult)
    assert result.volunteer_id == volunteer_id
    
    # Clean up any partial data
    try:
        qdrant_client = get_qdrant_client()
        await qdrant_client.delete_point(str(volunteer_id))
    except Exception:
        pass


@pytest.mark.asyncio
async def test_skills_extraction():
    """Test skills extraction from LinkedIn profile data."""
    dual_storage = await get_dual_storage_service()
    
    # Create profile with skills in different places
    profile = LinkedInProfileData(
        full_name="Skills Test User",
        skills=["Python", "JavaScript", "React"],
        projects=[
            {
                "name": "Test Project",
                "skills_used": ["Docker", "Kubernetes", "Python"]  # Python should be deduplicated
            }
        ]
    )
    
    # Extract skills
    skills = dual_storage._extract_skills_from_profile(profile)
    
    # Verify skills extraction
    assert isinstance(skills, list)
    assert "Python" in skills
    assert "JavaScript" in skills
    assert "React" in skills
    assert "Docker" in skills
    assert "Kubernetes" in skills
    
    # Should be deduplicated
    assert len(skills) == len(set(skills))


@pytest.mark.asyncio
async def test_new_schema_storage(sample_linkedin_profile):
    """Test storage using the new database schema with multi-vector support."""
    dual_storage = await get_dual_storage_service()
    db_client = await get_database_client()
    qdrant_client = get_qdrant_client()

    # Generate test data
    volunteer_id = uuid4()
    email = f"newschema.{str(volunteer_id)[:8]}@example.com"

    # Store profile using new architecture
    result = await dual_storage.store_linkedin_profile(
        volunteer_id=volunteer_id,
        email=email,
        profile_data=sample_linkedin_profile,
        source_url="https://www.linkedin.com/in/newschema/"
    )

    assert result.supabase_success and result.qdrant_success

    # Verify Supabase storage with new schema
    volunteer_data = await db_client.get_volunteer_by_id(volunteer_id)
    assert volunteer_data is not None

    # Check new schema fields
    assert "skills" in volunteer_data
    assert "vector_metadata" in volunteer_data
    assert "languages_spoken" in volunteer_data

    # Verify skills are stored as array
    skills = volunteer_data["skills"]
    assert isinstance(skills, list)
    assert len(skills) > 0
    assert "Python" in skills

    # Verify languages are stored as array
    languages = volunteer_data["languages_spoken"]
    assert isinstance(languages, list)
    assert "English" in languages

    # Verify vector metadata
    vector_metadata = volunteer_data["vector_metadata"]
    assert isinstance(vector_metadata, dict)
    assert "collection_name" in vector_metadata
    assert "vector_fields" in vector_metadata
    assert "vector_dimension" in vector_metadata
    assert vector_metadata["vector_dimension"] == 1536

    # Verify Qdrant storage with multi-vectors
    vector_data = await qdrant_client.get_point(str(volunteer_id))
    assert vector_data is not None

    # Check for multi-vector format
    if "vectors" in vector_data:
        vectors = vector_data["vectors"]
        assert isinstance(vectors, dict)
        # Should have all expected vector fields
        expected_fields = {
            'skills_vector', 'certifications_vector', 'experience_vector',
            'summary_vector', 'languages_vector', 'volunteer_experience_vector', 'combined_vector'
        }
        assert set(vectors.keys()).issubset(expected_fields)

    # Clean up
    try:
        await qdrant_client.delete_point(str(volunteer_id))
    except Exception as e:
        print(f"Cleanup warning: {e}")


@pytest.mark.asyncio
async def test_multi_vector_search():
    """Test multi-vector search functionality."""
    qdrant_client = get_qdrant_client()
    vector_service = await get_vector_service()

    # Create a sample profile for search
    search_profile = LinkedInProfileData(
        full_name="Search Test User",
        skills=["Python", "Data Science", "Machine Learning"],
        languages=["English", "French"]
    )

    # Generate vectors for search
    search_vectors = await vector_service.generate_multi_vector_embeddings(search_profile)

    # Test search with different vector fields
    for vector_field in ['skills_vector', 'combined_vector']:
        if vector_field in search_vectors:
            search_results = await qdrant_client.search_similar(
                query_vector=search_vectors[vector_field],
                vector_field=vector_field,
                top_k=5,
                score_threshold=0.0
            )

            # Results should be a list
            assert isinstance(search_results, list)
            # Each result should have expected structure
            for result in search_results:
                assert "id" in result
                assert "score" in result
                assert "payload" in result


@pytest.mark.asyncio
async def test_database_health_checks():
    """Test health checks for all database components."""
    # Test Supabase health
    db_client = await get_database_client()
    db_health = await db_client.health_check()
    assert "status" in db_health

    # Test Qdrant health
    qdrant_client = get_qdrant_client()
    qdrant_health = await qdrant_client.health_check()
    assert "status" in qdrant_health

    # Test vector service health
    vector_service = await get_vector_service()
    vector_health = await vector_service.health_check()
    assert "status" in vector_health


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
